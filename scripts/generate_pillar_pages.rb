#!/usr/bin/env ruby


require 'csv'
require 'fileutils'

INPUT_CSV = '../data/SEO_Pillar_Pages.csv'
OUTPUT_DIR = '../src/pages/resources'

# Helper to sanitize folder name
def clean_url_path(url)
  url.to_s.gsub(/^\//, '') # remove leading slash
end

CSV.foreach(INPUT_CSV, headers: true) do |row|
  url_path = clean_url_path(row['URL'])
  next if url_path.nil? || url_path.strip.empty?

  folder_path = File.join(OUTPUT_DIR, url_path)
  FileUtils.mkdir_p(folder_path)

  title = row['Pillar Page Title']
  broad_keyword = row['Broad keyword']
  long_tail_keyword = row['Long tail keyword']
  topic_cluster = row['Topic cluster']
  page_type = row['Page Type']
  keywords = long_tail_keyword.empty? ? broad_keyword : long_tail_keyword

  content = <<~ASTRO
    ---
    title: "#{title}"
    description: "Explore #{keywords} in the context of #{topic_cluster}."
    url: "#{row['URL']}"
    pageType: "#{page_type}"
    topicCluster: "#{topic_cluster}"
    keywords: "#{long_tail_keyword}"
    ---

    <h1>#{title}</h1>
    <p>This page focuses on <strong>#{broad_keyword}</strong> and explores topics such as: #{long_tail_keyword}.</p>
    <p>It is part of the <strong>#{topic_cluster}</strong> cluster.</p>
  ASTRO

  File.write(File.join(folder_path, 'index.astro'), content)
end

puts "Pillar pages generated in #{OUTPUT_DIR}"