#!/usr/bin/env ruby

require 'csv'
require 'fileutils'

INPUT_CSV = '../data/SEO_Blog_Posts.csv'
OUTPUT_DIR = '../src/pages/resources'

# Strip leading slash and split path
def extract_path_parts(url)
  url.to_s.gsub(/^\//, '').split('/')
end

CSV.foreach(INPUT_CSV, headers: true) do |row|
  path_parts = extract_path_parts(row['URL'])
  next if path_parts.empty?

  parent_dir = path_parts[0]
  post_file = "#{path_parts[1]}.astro"
  full_path = File.join(OUTPUT_DIR, parent_dir, post_file)
  FileUtils.mkdir_p(File.dirname(full_path))

  title = row['Blog Post Title']
  broad_keyword = row['Broad keyword']
  long_tail_keyword = row['Long tail keyword']
  topic_cluster = row['Topic cluster']
  page_type = row['Page Type']
  url = row['URL']
  keywords = long_tail_keyword.empty? ? broad_keyword : long_tail_keyword

  content = <<~ASTRO
    ---
    title: "#{title}"
    description: "Learn about #{keywords} within the #{topic_cluster} space."
    url: "#{url}"
    pageType: "#{page_type}"
    topicCluster: "#{topic_cluster}"
    keywords: "#{long_tail_keyword}"
    ---

    <h1>#{title}</h1>
    <p>This article explores <strong>#{long_tail_keyword}</strong> as part of our focus on <strong>#{topic_cluster}</strong>.</p>
    <p>It relates to the broader topic of <strong>#{broad_keyword}</strong>.</p>
  ASTRO

  File.write(full_path, content)
end

puts "Blog post pages generated under #{OUTPUT_DIR}"