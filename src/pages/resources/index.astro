---
import Layout from '../../layouts/Layout.astro';
import Header from '../../components/Header.astro';
import Footer from '../../components/Footer.astro';
---

<Layout title="Resources - TimeOut App" description="Helpful resources, guides, and tips for getting the most out of TimeOut App and improving your productivity.">
	<Header />
	<main class="pt-32 pb-20">
		<div class="container mx-auto px-4 max-w-6xl">
			<div class="text-center mb-16">
				<h1 class="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-primary-600 to-secondary-600 text-transparent bg-clip-text">Resources</h1>
				<p class="text-lg text-gray-700 max-w-2xl mx-auto">
					Everything you need to get the most out of TimeOut and build healthier digital habits.
				</p>
			</div>

			<!-- Resource Categories -->
			<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
				<!-- Getting Started -->
				<div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-all">
					<div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6 2.292m0-14.25v14.25"/></svg>
					</div>
					<h3 class="text-xl font-semibold mb-4">Getting Started Guide</h3>
					<p class="text-gray-700 mb-6">
						Learn how to set up your first app locks and start building better digital habits with TimeOut.
					</p>
					<a href="#getting-started" class="text-primary-600 hover:text-primary-700 font-medium">
						Read Guide →
					</a>
				</div>

				<!-- Best Practices -->
				<div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-all">
					<div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><path fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2l4-4m6 2a9 9 0 1 1-18 0a9 9 0 0 1 18 0"/></svg>
					</div>
					<h3 class="text-xl font-semibold mb-4">Best Practices</h3>
					<p class="text-gray-700 mb-6">
						Discover proven strategies and tips for maximizing your productivity and maintaining healthy screen time habits.
					</p>
					<a href="#best-practices" class="text-primary-600 hover:text-primary-700 font-medium">
						Learn More →
					</a>
				</div>

				<!-- Exercise Ideas -->
				<div class="bg-white rounded-2xl shadow-sm p-8 hover:shadow-md transition-all">
					<div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mb-6">
						<svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary-600" width="24" height="24" viewBox="0 0 24 24"><g fill="none" stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"><path d="M12 4a1 1 0 1 0 2 0a1 1 0 1 0-2 0M4 17l5 1l.75-1.5M15 21v-4l-4-3l1-6"/><path d="M7 12V9l5-1l3 3l3 1"/></g></svg>
					</div>
					<h3 class="text-xl font-semibold mb-4">Exercise Ideas</h3>
					<p class="text-gray-700 mb-6">
						Find inspiration for quick exercises and activities to unlock your apps and stay active throughout the day.
					</p>
					<a href="#exercise-ideas" class="text-primary-600 hover:text-primary-700 font-medium">
						Get Ideas →
					</a>
				</div>
			</div>

			<!-- Detailed Sections -->
			<div class="space-y-16">
				<!-- Getting Started Section -->
				<section id="getting-started" class="bg-white rounded-2xl shadow-sm p-8 md:p-12">
					<h2 class="text-2xl md:text-3xl font-bold mb-6">Getting Started with TimeOut</h2>
					
					<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
						<div>
							<h3 class="text-xl font-semibold mb-4">1. Download & Setup</h3>
							<ul class="space-y-3 text-gray-700 mb-6">
								<li class="flex items-start">
									<span class="text-primary-600 mr-2">•</span>
									Download TimeOut from the App Store
								</li>
								<li class="flex items-start">
									<span class="text-primary-600 mr-2">•</span>
									Grant necessary permissions for app monitoring
								</li>
								<li class="flex items-start">
									<span class="text-primary-600 mr-2">•</span>
									Connect your Health app for exercise tracking
								</li>
							</ul>

							<h3 class="text-xl font-semibold mb-4">2. Create Your First Lock</h3>
							<ul class="space-y-3 text-gray-700">
								<li class="flex items-start">
									<span class="text-primary-600 mr-2">•</span>
									Choose apps you want to limit (social media, games, etc.)
								</li>
								<li class="flex items-start">
									<span class="text-primary-600 mr-2">•</span>
									Set a realistic exercise goal (start with 5,000 steps)
								</li>
								<li class="flex items-start">
									<span class="text-primary-600 mr-2">•</span>
									Activate your lock and start moving!
								</li>
							</ul>
						</div>

						<div class="bg-gradient-to-br from-primary-50 to-secondary-50 rounded-xl p-6">
							<h4 class="font-semibold mb-3 text-primary-800">💡 Pro Tip</h4>
							<p class="text-gray-700">
								Start with just one or two apps and a modest step goal. As you build the habit, you can gradually increase your goals and add more apps to your locks.
							</p>
						</div>
					</div>
				</section>

				<!-- Best Practices Section -->
				<section id="best-practices" class="bg-white rounded-2xl shadow-sm p-8 md:p-12">
					<h2 class="text-2xl md:text-3xl font-bold mb-6">Best Practices for Success</h2>
					
					<div class="grid grid-cols-1 md:grid-cols-2 gap-8">
						<div>
							<h3 class="text-xl font-semibold mb-4">Setting Effective Goals</h3>
							<ul class="space-y-3 text-gray-700 mb-6">
								<li class="flex items-start">
									<span class="text-green-500 mr-2">✓</span>
									Start small and build gradually
								</li>
								<li class="flex items-start">
									<span class="text-green-500 mr-2">✓</span>
									Set goals based on your current activity level
								</li>
								<li class="flex items-start">
									<span class="text-green-500 mr-2">✓</span>
									Use different goals for weekdays vs weekends
								</li>
								<li class="flex items-start">
									<span class="text-green-500 mr-2">✓</span>
									Review and adjust goals weekly
								</li>
							</ul>

							<h3 class="text-xl font-semibold mb-4">Common Mistakes to Avoid</h3>
							<ul class="space-y-3 text-gray-700">
								<li class="flex items-start">
									<span class="text-red-500 mr-2">✗</span>
									Setting unrealistic step goals from day one
								</li>
								<li class="flex items-start">
									<span class="text-red-500 mr-2">✗</span>
									Locking too many apps at once
								</li>
								<li class="flex items-start">
									<span class="text-red-500 mr-2">✗</span>
									Ignoring your progress reports
								</li>
							</ul>
						</div>

						<div class="space-y-6">
							<div class="bg-blue-50 rounded-xl p-6">
								<h4 class="font-semibold mb-3 text-blue-800">📊 Track Your Progress</h4>
								<p class="text-gray-700">
									Use TimeOut's built-in reports to see how you're improving over time. Celebrate small wins and adjust your strategy based on what's working.
								</p>
							</div>

							<div class="bg-green-50 rounded-xl p-6">
								<h4 class="font-semibold mb-3 text-green-800">🎯 Stay Consistent</h4>
								<p class="text-gray-700">
									Consistency beats intensity. It's better to hit a smaller goal every day than to set a huge goal you can only achieve occasionally.
								</p>
							</div>
						</div>
					</div>
				</section>

				<!-- Exercise Ideas Section -->
				<section id="exercise-ideas" class="bg-white rounded-2xl shadow-sm p-8 md:p-12">
					<h2 class="text-2xl md:text-3xl font-bold mb-6">Quick Exercise Ideas</h2>
					
					<div class="grid grid-cols-1 md:grid-cols-3 gap-6">
						<div class="bg-gradient-to-br from-blue-50 to-blue-100 rounded-xl p-6">
							<h3 class="text-lg font-semibold mb-3 text-blue-800">🚶‍♀️ Walking</h3>
							<ul class="space-y-2 text-gray-700 text-sm">
								<li>• Take a walk around the block</li>
								<li>• Walk to a nearby coffee shop</li>
								<li>• Use stairs instead of elevators</li>
								<li>• Park further away from destinations</li>
							</ul>
						</div>

						<div class="bg-gradient-to-br from-green-50 to-green-100 rounded-xl p-6">
							<h3 class="text-lg font-semibold mb-3 text-green-800">🏃‍♂️ Quick Workouts</h3>
							<ul class="space-y-2 text-gray-700 text-sm">
								<li>• 5-minute bodyweight exercises</li>
								<li>• Jumping jacks or burpees</li>
								<li>• Yoga stretches</li>
								<li>• Dance to your favorite song</li>
							</ul>
						</div>

						<div class="bg-gradient-to-br from-purple-50 to-purple-100 rounded-xl p-6">
							<h3 class="text-lg font-semibold mb-3 text-purple-800">🎯 Daily Activities</h3>
							<ul class="space-y-2 text-gray-700 text-sm">
								<li>• Household chores</li>
								<li>• Gardening or yard work</li>
								<li>• Playing with pets</li>
								<li>• Walking while on phone calls</li>
							</ul>
						</div>
					</div>
				</section>
			</div>

			<!-- Call to Action -->
			<div class="mt-16 text-center bg-gradient-to-r from-primary-600 to-secondary-600 rounded-2xl p-8 md:p-12">
				<h2 class="text-2xl md:text-3xl font-bold text-white mb-4">Ready to Start Your Journey?</h2>
				<p class="text-white/90 mb-6 max-w-2xl mx-auto">
					Download TimeOut today and take the first step towards healthier digital habits and a more active lifestyle.
				</p>
				<a href="https://apps.apple.com/ie/app/time-out-lock-apps-for-focus/id6738120947" class="inline-block bg-white text-primary-600 px-8 py-3 rounded-full font-medium hover:bg-gray-100 transition-all">
					Download TimeOut
				</a>
			</div>
		</div>
	</main>
	<Footer />
</Layout>
