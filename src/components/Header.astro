---
---

<header class="fixed w-full z-50 bg-white/80 backdrop-blur-md shadow-sm">
  <div class="container mx-auto px-4 py-4 flex justify-between items-center">
    <a href="#hero-header" class="flex items-center">
      <span class="text-2xl font-bold bg-gradient-to-r from-primary-600 to-secondary-600 text-transparent bg-clip-text">TimeOut</span>
    </a>

    <nav class="hidden md:flex space-x-8">
      <a href="#about" class="text-gray-700 hover:text-primary-600 transition-colors">About</a>
      <a href="#features" class="text-gray-700 hover:text-primary-600 transition-colors">Features</a>
      <a href="/resources" class="text-gray-700 hover:text-primary-600 transition-colors">Resources</a>
    </nav>

    <a href="https://apps.apple.com/ie/app/time-out-lock-apps-for-focus/id6738120947" class="hidden md:block bg-gradient-to-r from-primary-600 to-secondary-600 text-white px-6 py-2 rounded-full font-medium hover:shadow-lg transition-all">
      Download
    </a>

    <!-- Mobile menu button -->
    <button id="mobile-menu-button" class="md:hidden text-gray-700 focus:outline-none">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
      </svg>
    </button>
  </div>

  <!-- Mobile menu -->
  <div id="mobile-menu" class="md:hidden hidden bg-white shadow-lg absolute w-full">
    <div class="container mx-auto px-4 py-4 flex flex-col space-y-4">
      <a href="#about" class="text-gray-700 hover:text-primary-600 transition-colors">About</a>
      <a href="#features" class="text-gray-700 hover:text-primary-600 transition-colors">Features</a>
      <a href="/resources" class="text-gray-700 hover:text-primary-600 transition-colors">Resources</a>
      <a href="https://apps.apple.com/ie/app/time-out-lock-apps-for-focus/id6738120947" class="bg-gradient-to-r from-primary-600 to-secondary-600 text-white px-6 py-2 rounded-full font-medium text-center">
        Download
      </a>
    </div>
  </div>
</header>

<script>
  // Mobile menu toggle
  const mobileMenuButton = document.getElementById('mobile-menu-button');
  const mobileMenu = document.getElementById('mobile-menu');

  if (mobileMenuButton && mobileMenu) {
    mobileMenuButton.addEventListener('click', () => {
      mobileMenu.classList.toggle('hidden');
    });

    // Close mobile menu when clicking on a link
    const mobileLinks = mobileMenu.querySelectorAll('a');
    mobileLinks.forEach(link => {
      link.addEventListener('click', () => {
        mobileMenu.classList.add('hidden');
      });
    });
  }
</script>
